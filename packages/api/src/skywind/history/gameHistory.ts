import { col, FindOptions, WhereOptions } from "sequelize";
import * as FilterService from "../services/filter";
import { sequelizeFindToStoredFunctionInput, StoredFunctionInput } from "../services/filter";
import {
    EventHistory,
    EventHistoryExtraDetails,
    GameHistoryVisualisation,
    GameHistoryVisualizationDetailsTokenData,
    GameReplayDetails,
    GameVersionDetails,
    GHVisualizationOptions,
    HISTORY_URL_REPLACE_STRING,
    HISTORY_URL_SEARCH_STRING,
    HistoryInfo,
    RoundHistory
} from "../entities/gameHistory";
import config from "../config";
import { PagingHelper } from "../utils/paginghelper";
import { getSpinHistoryModel, SpinHistoryMapper } from "../models/spinHistory";
import { findGameInitSettings } from "./gameInitSettings";
import * as Errors from "../errors";
import * as GameService from "../services/game";
import { getEntitySettings } from "../services/settings";
import { BaseEntity } from "../entities/entity";
import { getRoundHistoryServiceFactory } from "./gameHistoryServiceFactory";
import {
    CreatedGameHistoryTokenData,
    generateGameHistoryVisualizationToken,
    verifyGameHistoryVisualizationToken
} from "../utils/token";
import { getDynamicEntityDomainService } from "../services/entityDomainService";
import EntityCache from "../cache/entity";
import { sequelizeSlave as db } from "../storage/db";
import { buildHistoryUrl, isItgMiniGsGame } from "../services/domain";
import {
    decorateFindOptionsWithLimitOffset,
    decorateQueryWithTs,
    decorateWhereOptionsWithIsPayment
} from "./decorators";
import { buildSpinHistoryQuery, SPIN_HISTORY_QUERY, spinHistoryMapping } from "./rawQueries";
import { BrandEntity } from "../entities/brand";
import * as MerchantCache from "../cache/merchant";
import * as appendQuery from "append-query";
import { PlayMode, SmResultExtraData, SmResultTransaction, WALLET_TYPE } from "@skywind-group/sw-wallet-adapter-core";
import { EntityGame, EntityGameInfo, Game } from "../entities/game";
import { getEntitySettingsForHistory } from "./gameHistoryV2";
import { getSpinHistoryUsingStoredProcedure } from "./spinHistory";
import { createSmResult } from "@skywind-group/sw-sm-result-builder";
import logger from "../utils/logger";
import { getEntityJurisdictionService } from "../services/entityJurisdiction";
import { Jurisdiction } from "../entities/jurisdiction";
import { CurrencyFormatConfig, CurrencyFormatSettings, EntitySettings } from "../entities/settings";
import {
    buildGameSettings,
    decorateStartGameDataWithBrandSettings,
    decorateStartGameDataWithGameSettings,
    decorateStartGameDataWithRTP,
    decorateStartGameResultWithMerchantSpecificData,
    getMerchantGameSettings
} from "../services/playService";
import { Merchant } from "../entities/merchant";
import { getGameClientVersionService } from "../services/gameVersionService";
import { BrandSettings, StartGameSettings } from "../entities/gameprovider";

const log = logger("game-history");

export const historyQueryField = {
    "brandId": "brand_id",
    "roundId": "round_id",
    "playerCode": "player_code",
    "gameCode": "game_code",
    "currency": "currency_code",
    "firstTs": "first_ts",
    "ts": "last_ts",
    "finished": "is_finished",
    "bet": "bet",
    "win": "win",
    "revenue": "revenue",
    "device": "device_code",
    "balanceBefore": "start_balance",
    "balanceAfter": "end_balance",
    "isTest": "is_test",
    "recoveryType": "recovery_type"
};
export const queryParamsKeys = [
    ...Object.keys(historyQueryField),
    "sortBy",
    "sortOrder",
    "offset",
    "limit"
];

export const sortableKeys = [
    "brandId",
    "roundId",
    "playerCode",
    "gameCode",
    "currencyCode",
    "ts",
    "firstTs",
    "finished",
    "bet",
    "win",
    "revenue",
    "isTest"
];

export const roundHistorySortableKeys = [
    "ts",
    "bet",
    "win",
    "spinNumber"
];

export const roundsHistoryMapping = {
    roundId: "id",
    brandId: "brand_id",
    playerCode: "player_code",
    gameId: "game_id",
    gameCode: "game_code",
    device: "device_id",
    currency: "currency",
    win: "total_win",
    bet: "total_bet",
    ts: "finished_at",
    balanceBefore: "balance_before",
    balanceAfter: "balance_after",
    firstTs: "started_at",
    isTest: "test",
    insertedAt: "inserted_at",
    revenue: "(total_bet - total_win)",
    finished: "finished_at is NOT NULL",
    recoveryType: "recovery_type"
};

export const roundHistoryQueryKeysForRoundIdQueries = [
    "roundId",
    "firstTs",
    "ts",
    "sortBy",
    "sortOrder",
    "offset",
    "limit"
];

const eventsFilterKeys = ["type", "ts", "gameCode", "playerCode", "roundId", "insertedAt", "isHidden"];

export interface SpinDetailsParams {
    addExtraDataForGHApp?: boolean;
    includeTrxId?: boolean;
    hideBalanceBeforeAndAfter?: boolean;
    playerCode?: string;
    decorateQueryWithRoundTimestamps?: boolean;
    mode?: string;
    addJurisdictionSettings?: boolean;
    currencyFormatSettings?: CurrencyFormatSettings;
    isReplayMode?: boolean;
}

export interface GameHistorySpinDetails {
    roundId: number;
    spinNumber: number;
    device: string;
    type: string;
    currency: string;
    currencyFormatConfig?: CurrencyFormatConfig;
    bet: number;
    win: number;
    balanceBefore: number;
    balanceAfter: number;
    endOfRound: boolean;
    ts: string;
    test: boolean;
    isPayment: boolean;
    details: string;
    gameId: string;
    gameVersion: string;
    gameCode?: string;
    totalJpContribution?: number;
    totalJpWin?: number;
    credit?: number;
    debit?: number;
    insertedAt?: any;
    playerCode?: string;
    trxId?: string;
    extraData?: any;
    isHidden?: boolean;
}

type DBRow = EventHistory & {
    insertedAt?: string;
};

function fromDBRow(row: DBRow, params: SpinDetailsParams = {}): GameHistorySpinDetails {
    const spin: GameHistorySpinDetails = {
        roundId: +row.roundId,
        spinNumber: +row.spinNumber,
        device: row.device,
        type: row.type,
        currency: row.currency,
        bet: +row.bet,
        win: +row.win,
        balanceBefore: row.balanceBefore && !params.hideBalanceBeforeAndAfter ? +row.balanceBefore : undefined,
        balanceAfter: row.balanceAfter && !params.hideBalanceBeforeAndAfter ? +row.balanceAfter : undefined,
        endOfRound: row.endOfRound,
        ts: row.ts,
        test: row.test,
        isPayment: row.isPayment,
        details: row.result,
        gameId: row.gameId,
        gameVersion: row.gameVersion,
        gameCode: params.addExtraDataForGHApp ? row.gameCode : undefined,
        totalJpContribution: +row.totalJpContribution,
        totalJpWin: +row.totalJpWin,
        credit: +row.credit,
        debit: +row.debit,
        insertedAt: row.insertedAt,
        playerCode: row.playerCode,
        trxId: params.includeTrxId ? row.walletTransactionId : undefined,
        extraData: row.extraData ? row.extraData : undefined,
        isHidden: row.isHidden
    };
    if (spin.currency && params.currencyFormatSettings) {
        const currencyFormatConfig = params.currencyFormatSettings[spin.currency];
        if (currencyFormatConfig) {
            spin.currencyFormatConfig = currencyFormatConfig;
        }
    }
    return spin;
}

export async function findGameHistoryEntries(brandId: number | BaseEntity,
                                             query?: WhereOptions<any>): Promise<RoundHistory[]> {
    return getRoundHistoryServiceFactory().getHistoryServiceV1().getRounds(brandId, query);
}

export function mapRoundRowAndFixPrecision(round): RoundHistory {
    if (!round.balanceBefore && Number(round.balanceAfter) === 0) {
        round.balanceBefore = round.balanceAfter;
    }

    // Fix returning balanceBefore and balanceAfter as string
    if (round.balanceBefore) {
        round.balanceBefore = +round.balanceBefore;
    }
    if (round.balanceAfter) {
        round.balanceAfter = +round.balanceAfter;
    }

    if (round.revenue) {
        round.revenue = +(+round.revenue).toFixed(4);
    }
    if (round.win) {
        round.win = +(+round.win).toFixed(4);
    }
    if (round.bet) {
        round.bet = +(+round.bet).toFixed(4);
    }

    if (!Number.isNaN(+round.roundId)) {
        round.roundId = +round.roundId;
    }

    round.totalJpContribution = +round.totalJpContribution || 0;
    round.totalJpWin = +round.totalJpWin || 0;

    if (round.credit) {
        round.credit = +(+round.credit).toFixed(4);
    }
    if (round.debit) {
        round.debit = +(+round.debit).toFixed(4);
    }
    if (round.extraData === null) {
        delete round.extraData;
    }

    return round;
}

export async function getGameHistoryDetails(entity: BaseEntity,
                                            roundId: number,
                                            spinNumber: number,
                                            params: SpinDetailsParams = {}): Promise<EventHistoryExtraDetails> {
    const whereOptions: WhereOptions<any> = { brandId: entity.id, roundId, spinNumber };
    if (params.playerCode) {
        whereOptions.playerCode = params.playerCode;
    }

    const model = getSpinHistoryModel();
    let spin;
    if (config.gameHistory.useStoredProcedures) {
        await decorateQueryWithTs(whereOptions as any, {} as any, findGameHistoryEntries);
        const mapper: SpinHistoryMapper<EventHistory> = item => item;
        const sql = buildSpinHistoryQuery({ where: whereOptions });
        [spin] = await PagingHelper.findAllNative(
            "SELECT * " + sql,
            db,
            model,
            1,
            0,
            mapper
        );
    } else {
        spin = await model.findOne({ where: whereOptions });
    }
    if (!spin) {
        return Promise.reject(new Errors.GameHistoryDetailsNotFound());
    }

    const { hideBalanceBeforeAndAfter, currencyFormatSettings } = await getEntitySettingsForHistory(entity);
    return await decorateSpinData(spin, entity, roundId, spinNumber, {
        ...params,
        hideBalanceBeforeAndAfter,
        currencyFormatSettings
    });
}

export async function getEvents(entity: BaseEntity,
                                reqQuery: any = {},
                                params: SpinDetailsParams = {}): Promise<GameHistorySpinDetails[]> {

    const whereOptions: WhereOptions<any> = FilterService.parseFilter(reqQuery, eventsFilterKeys);
    whereOptions["brandId"] = entity.id;
    const defaultSort: string = params.addExtraDataForGHApp ? "ASC" : "DESC";

    if (!params.addExtraDataForGHApp) {
        decorateWhereOptionsWithIsPayment(whereOptions, reqQuery);
    }

    if (params.decorateQueryWithRoundTimestamps) {
        await decorateQueryWithTs(whereOptions as any, reqQuery, findGameHistoryEntries);
    }

    const sortBy = FilterService.getSortKey(reqQuery, roundHistorySortableKeys, "ts");
    const sortOrder = FilterService.valueFromQuery(reqQuery, "sortOrder") || defaultSort;
    const offset = FilterService.valueFromQuery(reqQuery, "offset", false) || 0;
    const limit = FilterService.valueFromQuery(reqQuery, "limit", false) || 1000;
    const findOptions: FindOptions<any> = {
        where: whereOptions,
        order: [[col(sortBy), sortOrder]],
        offset,
        limit
    };

    if (!params.addExtraDataForGHApp) {
        decorateFindOptionsWithLimitOffset(findOptions, reqQuery);
    }

    /**
     * It needs only for replay mode. We should always receive all spins without limitation
     */
    if (params.isReplayMode) {
        const MAX_INTEGER = 2147483647;
        findOptions["limit"] = MAX_INTEGER;
    }
    const entitySettings = await getEntitySettingsForHistory(entity);
    const mapper = await getMapper(entity as BrandEntity, {
        ...params,
        hideBalanceBeforeAndAfter: entitySettings.hideBalanceBeforeAndAfter,
        currencyFormatSettings: entitySettings.currencyFormatSettings
    });

    let spinsDetails: GameHistorySpinDetails[];
    if (config.gameHistory.useStoredProcedures) {
        spinsDetails = await getSpinHistoryUsingStoredProcedure(findOptions, mapper) as any;
    } else {
        spinsDetails = await PagingHelper.findAndCountAll(getSpinHistoryModel(), findOptions, mapper as any);
    }

    if (spinsDetails.length && params.addExtraDataForGHApp) {
        const initSettings = await findGameInitSettings(spinsDetails[0].gameId, spinsDetails[0].gameVersion);
        // under settings object there is limits data which may be incorrect due to we don't track and update it
        // see SWS-2389
        if (initSettings) {
            delete initSettings["settings"];
        }

        const mode = reqQuery.mode || params.mode;
        const gameCode = spinsDetails[0].gameCode;

        const historyInfo = mode === "ghm" ?
            await getGameHistoryModuleHistoryInfo(gameCode, entity) :
            await getHistoryInfo(gameCode, entity, mode);

        const jrsd = await findJurisdictionSettings(entity.id);
        const jrsdSettings = jrsd?.settings;

        spinsDetails.forEach((spin, i) => {
            spinsDetails[i] = {
                ...spin,
                initSettings,
                historyInfo,
                jrsdSettings,
            } as any;
        });
    }

    return spinsDetails;
}

async function getEventHistoryForSmResult(entity: BaseEntity,
                                          reqQuery: any = {},
                                          params: SpinDetailsParams = {}): Promise<GameHistorySpinDetails[]> {

    const whereOptions: WhereOptions<any> = FilterService.parseFilter(reqQuery, ["roundId", "ts"]);
    whereOptions["brandId"] = entity.id;

    const sortBy = FilterService.getSortKey(reqQuery, roundHistorySortableKeys, "ts");
    const sortOrder: any = "ASC";
    const offset = FilterService.valueFromQuery(reqQuery, "offset") || 0;
    const limit = FilterService.valueFromQuery(reqQuery, "limit") || 1000;
    const findOptions: FindOptions<any> = {
        where: whereOptions,
        order: [[col(sortBy), sortOrder]],
        offset,
        limit,
    };

    const mapper = await getMapper(entity as BrandEntity, params);

    let spinsDetails: GameHistorySpinDetails[];
    if (config.gameHistory.useStoredProcedures) {
        const funcInput: StoredFunctionInput = sequelizeFindToStoredFunctionInput(findOptions, spinHistoryMapping);
        const queryWithParams: string = SPIN_HISTORY_QUERY.replace("$whereFilters", () => funcInput.whereFilters)
            .replace("$sortBy", funcInput.sortOrder);
        spinsDetails = await PagingHelper.findAndCountAllNative("SELECT * " + queryWithParams,
            "SELECT COUNT(*) " + queryWithParams,
            db,
            getSpinHistoryModel(),
            funcInput.limit || 1000,
            funcInput.offset || 0,
            mapper as any);
    } else {
        spinsDetails = await PagingHelper.findAndCountAll(getSpinHistoryModel(), findOptions, mapper as any);
    }

    return spinsDetails;
}

async function getSmResultBySpinsDetails(rawSpinsDetails: GameHistorySpinDetails[]) {
    const spinsDetails = rawSpinsDetails && rawSpinsDetails.filter(e => Object.keys(e.details).length !== 0);
    if (spinsDetails.length) {
        spinsDetails.map(e => {
            e["result"] = e.details;
        });
        const gameId = spinsDetails[0].gameId;
        const initSettings = await findGameInitSettings(gameId, spinsDetails[0].gameVersion);
        try {
            return createSmResult(spinsDetails, initSettings, gameId);
        } catch (e) {
            log.error(e, "Error during creation of smResult");
            return undefined;
        }
    }
    return undefined;
}

async function getSmResultExtraDataBySpinsDetails(spinsDetails: GameHistorySpinDetails[]): Promise<SmResultExtraData> {
    if (spinsDetails && spinsDetails.length) {
        const gameId = spinsDetails[0].gameId;
        const initSettings = await findGameInitSettings(gameId, spinsDetails[0].gameVersion) as any;
        const gameTitle: string = initSettings && initSettings.name;
        const linesDefinition = initSettings && initSettings.slot && initSettings.slot.linesDefinition;
        const lines: number = linesDefinition?.fixedMultiplierForTotalBet || linesDefinition?.fixedLinesCount;
        const transactions: SmResultTransaction[] = [];
        spinsDetails.forEach(spin => {
            transactions.push({
                transactionId: spin.trxId,
                type: "bet",
                amount: spin.bet,
                ts: spin.insertedAt
            });
            transactions.push({
                transactionId: spin.trxId,
                type: "win",
                amount: spin.win,
                ts: spin.insertedAt
            });
        });
        return { gameId, gameTitle, lines, transactions };
    }
    return undefined;
}

export async function getSmResult(entity: BaseEntity, reqQuery: any = {}): Promise<string> {
    const spinsDetails = await getEventHistoryForSmResult(entity, reqQuery);
    return getSmResultBySpinsDetails(spinsDetails);
}

interface SmResultWithExtraData {
    smResult: string;
    smResultExtraData: SmResultExtraData;
}

export async function getSmResultWithExtraData(entity: BaseEntity, reqQuery: any = {}): Promise<SmResultWithExtraData> {
    const spinsDetails = await getEventHistoryForSmResult(entity, reqQuery, { includeTrxId: true });
    const smResult = await getSmResultBySpinsDetails(spinsDetails);
    const smResultExtraData = await getSmResultExtraDataBySpinsDetails(spinsDetails);
    return { smResult, smResultExtraData };
}

export async function decorateSpinData(spin: EventHistory,
                                       entity: BaseEntity,
                                       roundId: number,
                                       spinNumber: number,
                                       params: SpinDetailsParams = {}): Promise<EventHistoryExtraDetails> {
    const initSettings = await findGameInitSettings(spin.gameId, spin.gameVersion);
    // under settings object there is limits data which may be incorrect due to we don't track and update it
    // see SWS-2389
    if (initSettings) {
        delete initSettings["settings"];
    }

    const historyInfo = params.mode === "ghm" ?
        await getGameHistoryModuleHistoryInfo(spin.gameCode, entity) :
        await getHistoryInfo(spin.gameCode, entity, params.mode);

    const details: EventHistoryExtraDetails = {
        roundId: +roundId,
        spinNumber: +spinNumber,
        gameId: spin.gameId,
        gameCode: spin.gameCode,
        gameVersion: spin.gameVersion,
        details: spin.result,
        initSettings,
        ts: spin.ts,
        historyInfo,
        totalJpContribution: +spin.totalJpContribution,
        totalJpWin: +spin.totalJpWin,
        credit: +spin.credit,
        debit: +spin.debit,
        extraData: spin.extraData,
        currency: spin.currency
    };

    if (spin.currency && params.currencyFormatSettings) {
        const currencyFormatConfig = params.currencyFormatSettings[spin.currency];
        if (currencyFormatConfig) {
            details.currencyFormatConfig = currencyFormatConfig;
        }
    }

    if (params.addExtraDataForGHApp) {
        details.type = spin.type;
        if (!params.hideBalanceBeforeAndAfter) {
            details.balanceBefore = spin.balanceBefore ? +spin.balanceBefore : undefined;
            details.balanceAfter = spin.balanceAfter ? +spin.balanceAfter : undefined;
        }
        details.win = +spin.win;
        details.bet = +spin.bet;
        details.device = spin.device;
    }

    if (params.addJurisdictionSettings) {
        const jrsd = await findJurisdictionSettings(entity.id);
        details.jrsdSettings = jrsd?.settings;
    }

    return details;
}

async function getHistoryInfo(gameCode: string, entity: BaseEntity, mode?: string): Promise<HistoryInfo> {
    const game = await GameService.findOne({
        code: gameCode,
    });

    const url = await game.getHistoryUrl(entity, mode);
    return {
        url,
        historyRenderType: game.historyRenderType
    };
}

async function getGameHistoryModuleHistoryInfo(gameCode: string, entity: BaseEntity): Promise<HistoryInfo> {
    const game = await GameService.findOne({
        code: gameCode,
    });

    const url = await game.getGameHistoryModuleUrl(entity);
    return {
        url,
        historyRenderType: game.historyRenderType
    };
}

export async function getGameVersion(gameCode: string,
                                     gameVersion: string,
                                     brandId?: number): Promise<GameVersionDetails> {

    const game = await GameService.findOne({
        code: gameCode,
    });

    const initSettings = await findGameInitSettings(game.providerGameCode, gameVersion);
    if (!initSettings && !isItgMiniGsGame(game)) {
        return Promise.reject(new Errors.GameNotFoundError(gameCode));
    }

    const jrsd = await findJurisdictionSettings(brandId);

    const entitySettings = await getEntitySettingsForHistory(brandId);
    if (entitySettings?.currencyFormatSettings) {
        initSettings.currencyFormatSettings = entitySettings.currencyFormatSettings;
    }

    const response: GameVersionDetails = {
        gameId: game.providerGameCode,
        gameVersion: gameVersion,
        initSettings,
        jrsdSettings: jrsd?.settings,
    };

    if (brandId) {
        const entity: BaseEntity = await EntityCache.findOne({ id: brandId });
        const url = await game.getGameHistoryModuleUrl(entity);
        response.historyInfo = { url };
    }

    return response;
}

export async function getGameVersionForReplayMode(gameCode: string,
                                                  gameVersion: string,
                                                  brandId?: number,
                                                  currency?: string): Promise<GameVersionDetails> {

    const game = await GameService.findOne({
        code: gameCode,
    });

    const initSettings = await findGameInitSettings(game.providerGameCode, gameVersion);
    if (!initSettings && !isItgMiniGsGame(game)) {
        return Promise.reject(new Errors.GameNotFoundError(gameCode));
    }

    const jrsd = await findJurisdictionSettings(brandId);

    const entitySettings = await getEntitySettingsForHistory(brandId);
    if (entitySettings?.currencyFormatSettings) {
        initSettings.currencyFormatSettings = entitySettings.currencyFormatSettings;
    }

    let response: GameReplayDetails = {
        gameId: game.providerGameCode,
        gameVersion: gameVersion,
        initSettings,
        jrsdSettings: jrsd?.settings,
        jurisdictionCode: jrsd?.code,
    };

    const brand: BrandEntity = await EntityCache.findOne({ id: brandId });
    const entityGame: EntityGame = await GameService.findOneEntityGame(brand, gameCode);

    let result: { settings?: StartGameSettings; brandSettings?: BrandSettings; };
    if (brand.isMerchant) {
        result = await merchantGameSettings(brand, entityGame, entitySettings, currency);
    } else {
        result = await brandGameSettings(brand, entityGame, entitySettings, currency);
    }

    response = {
        ...response,
        ...result,
    };

    const entity = await EntityCache.findOne({ id: entityGame.entityId });
    const gameClientVersion = await getGameClientVersionService().getGameClientVersionItem(entity, entityGame);
    await decorateStartGameDataWithBrandSettings(
        entitySettings,
        entityGame,
        response as any,
        PlayMode.REAL,
        false,
        gameClientVersion,
        currency
    );
    decorateStartGameDataWithGameSettings(entityGame, response as any, gameClientVersion);
    decorateStartGameDataWithRTP(entityGame, entitySettings, response as any);

    return response;
}

async function brandGameSettings(brand: BrandEntity,
                                 entityGame: EntityGame,
                                 settings: EntitySettings,
                                 currency: string) {

    // TODO: maybe need to retrieve actual limits
    const mockLimits = {};
    const gameSettings = await buildGameSettings(
        brand,
        entityGame,
        settings,
        PlayMode.REAL,
        mockLimits,
        currency
    );
    return {
        settings: gameSettings,
    };
}

async function merchantGameSettings(brand: BrandEntity,
                                    entityGame: EntityGame,
                                    settings: EntitySettings,
                                    currency: string) {
    const merchant: Merchant = await MerchantCache.findOne(brand);

    // TODO: maybe need to retrieve actual limits
    const mockLimits = {};
    const gameSettings = await getMerchantGameSettings(brand,
        entityGame,
        settings,
        merchant,
        PlayMode.REAL,
        mockLimits,
        currency
    );

    const dataMock: any = { response: {} };
    // TODO: regulatoryData needs for Italian regulation
    const gameTokenMock: any = {};
    decorateStartGameResultWithMerchantSpecificData(dataMock, merchant, gameTokenMock);

    return {
        settings: gameSettings,
        brandSettings: dataMock.response.brandSettings || {}
    };
}

const HASH_REPLACE_STRING = "%23";
const HASH_SYMBOL = "#";

/**
 * Returns
 */
export async function getGameHistoryDetailsImageUrl(
    entity: BaseEntity,
    roundId: number,
    options: GHVisualizationOptions = {}): Promise<GameHistoryVisualisation> {

    const jwtData: GameHistoryVisualizationDetailsTokenData = {
        eId: entity.id,
        rId: roundId,
        firstTs: options.firstTs,
        ts: options.ts
    };

    if (options.spinNumber) {
        jwtData.sId = options.spinNumber;
    }

    if (options.timezone) {
        jwtData.timezone = options.timezone;
    }

    let game: Game;
    if (options.gameCode) {
        const entityGame: EntityGame = await GameService.findOneEntityGame(entity, options.gameCode);
        game = entityGame && entityGame.game;
    }

    const settings = await getEntitySettings(entity.path);
    const isGameHistory2 = !!(game && game.historyRenderType >= 3);
    const historyKeyName = isGameHistory2 ? "history2_url" : "history_url";
    const url = settings?.urlParams?.[historyKeyName];

    if (!url) {
        return Promise.reject(new Errors.GameHistoryUrlNotFoundError());
    }

    const historyApp = await buildHistoryUrl(url, entity);
    const historyAppUrl = isGameHistory2 ? historyApp : historyApp
        .replace(HISTORY_URL_SEARCH_STRING, HISTORY_URL_REPLACE_STRING);

    const dynamicDomain = await getDynamicEntityDomainService().get(entity);
    if (!dynamicDomain) {
        return Promise.reject(new Errors.DomainNotFoundError());
    }
    const { token, ttl: tokenTTL }: CreatedGameHistoryTokenData =
        await generateGameHistoryVisualizationToken(jwtData, options.ttl);

    const gameHistory2QueryParams = isGameHistory2 ? await getGH2QueryParams(game, entity) : {};

    const queryParams = {
        ...settings.ghAppSettings?.urlParams,
        data: token,
        url: isGameHistory2 ? `${config.gameHistory.gameServerSchema}://${dynamicDomain.domain}` : dynamicDomain.domain,
        language: options.language,
        currency: options.currency,
        showRoundInfo: options.showRoundInfo,
        ...gameHistory2QueryParams,
    };

    // We're replacing "#" sign with "%23" string in order to work around the way how appendQuery lib which treats
    // this symbol by placing it to the end of url without regard of original place.
    // After appendQuery function was called we return "#" back.
    const urlHashReplaced = historyAppUrl.replace(HASH_SYMBOL, HASH_REPLACE_STRING);
    const imageUrl = appendQuery(urlHashReplaced, queryParams, { removeNull: true })
        .replace(HASH_REPLACE_STRING, HASH_SYMBOL);

    return {
        imageUrl,
        ttl: tokenTTL
    };
}

async function getGH2QueryParams(game: Game, entity: BaseEntity) {
    if (game) {
        const historyUrl = await buildHistoryUrl(game.url.substring(0, game.url.indexOf("?")), entity, game);
        return {
            historyUrl: historyUrl.replace(HISTORY_URL_SEARCH_STRING, HISTORY_URL_REPLACE_STRING),
            gameName: game.title
        };
    } else {
        return {};
    }
}

export async function getGameHistoryDetailsForGHApp(token: string): Promise<EventHistoryExtraDetails> {
    const data: GameHistoryVisualizationDetailsTokenData = await verifyGameHistoryVisualizationToken(token);
    const entity: BaseEntity = await EntityCache.findOne({ id: data.eId });
    if (!entity) {
        return Promise.reject(new Errors.EntityCouldNotBeFound());
    }

    return await getGameHistoryDetails(entity, data.rId, data.sId, {
        addExtraDataForGHApp: true,
        mode: "ghm",
        addJurisdictionSettings: true
    });
}

export async function getRoundHistoryDetailsForGHApp(token: string, types?: string) {
    const data: GameHistoryVisualizationDetailsTokenData = await verifyGameHistoryVisualizationToken(token);
    const entity: BaseEntity = await EntityCache.findOne({ id: data.eId });
    if (!entity) {
        return Promise.reject(new Errors.EntityCouldNotBeFound());
    }

    const query: any = {
        roundId: data.rId,
        type__in: types,
    };
    if (data.firstTs) {
        query.ts__gte = data.firstTs;
    }
    if (data.ts) {
        query.ts__lte = data.ts;
    }

    return getEvents(entity, query, { addExtraDataForGHApp: true, mode: "ghm" });
}

async function getMapper(
    entity: BrandEntity,
    params: SpinDetailsParams = {}): Promise<(row: DBRow) => GameHistorySpinDetails> {

    const defaultMapper = (row: DBRow) => fromDBRow(row, params);

    if (!entity.isMerchant) {
        return defaultMapper;
    }

    const merchant = await MerchantCache.findOne(entity);
    if (merchant.params.walletType === WALLET_TYPE.UPDATE_WIN_ON_ROUND_FINISHED || merchant.params.walletType === WALLET_TYPE.UPDATE_WIN_ON_ROUND_FINISHED_WITH_INTERMEDIATE_PHASES) {
        return (row: any) => {
            const spin = defaultMapper(row);
            if (spin.endOfRound) {
                if (spin.spinNumber === 0) {
                    return spin;
                } else {
                    return {
                        ...spin,
                        balanceBefore: undefined
                    };
                }
            }

            if (spin.spinNumber === 0) {
                if (merchant.params.walletType === WALLET_TYPE.UPDATE_WIN_ON_ROUND_FINISHED_WITH_INTERMEDIATE_PHASES) {
                    return spin;
                }
                return {
                    ...spin,
                    balanceAfter: undefined
                };
            }

            return {
                ...spin,
                balanceAfter: undefined,
                balanceBefore: undefined
            };
        };
    }

    return defaultMapper;
}

export async function getSpinListByBrand(entity: BaseEntity,
                                         reqQuery: any,
                                         params: SpinDetailsParams = {}): Promise<GameHistorySpinDetails[]> {
    const events = await getEvents(entity, reqQuery, params);

    // Filter result
    for (const event of events) {
        (event as any)["details"] = undefined;
    }

    return events;
}

export async function getRoundInfoForGHApp(token: string): Promise<RoundHistory> {
    const data = await verifyGameHistoryVisualizationToken(token);

    const [round] = await findGameHistoryEntries(data.eId, { roundId: data.rId });

    if (!round) {
        throw new Errors.ResourceNotFoundError("Round not found");
    }

    return round;
}

export function appendGameNameToRounds(rounds: RoundHistory[],
                                       games: EntityGameInfo[]): (RoundHistory & { gameName: string })[] {
    const updatedRounds: (RoundHistory & { gameName: string })[] = [];

    for (const round of rounds) {
        const game = games.find(item => item.code === round.gameCode);
        updatedRounds.push({
            ...round,
            gameName: game && game.title
        });
    }

    return updatedRounds;
}

async function findJurisdictionSettings(entityId: number): Promise<Jurisdiction | undefined> {
    const [jurisdiction] = await getEntityJurisdictionService().findAll({ entityId });
    if (!jurisdiction) {
        return undefined;
    }
    return jurisdiction;
}
